# 🐛 Bug Tracker with Basecamp Integration

A modern, mobile-responsive bug tracking web application that automatically creates To-dos in Basecamp when bugs are submitted. Built with React, Node.js, and the Basecamp 4 API.

## ✨ Features

- **Simple Bug Submission**: Clean, mobile-first form for reporting bugs
- **Basecamp Integration**: Automatically creates To-dos in your Basecamp project
- **Screenshot Support**: Upload screenshots with drag-and-drop functionality
- **Priority Levels**: Low, Medium, High priority with visual indicators
- **Real-time Status**: Shows server and authentication status
- **Responsive Design**: Mobile-optimized with Slack/Linear inspired UI
- **View Submitted Bugs**: Optional read-only list of submitted bugs

## 🏗️ Architecture

- **Frontend**: React 19 + Vite for modern, fast development
- **Backend**: Node.js + Express as secure API proxy
- **Integration**: Basecamp 4 API with OAuth 2.0 authentication
- **Security**: All credentials stored server-side, no sensitive data in frontend

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm
- Basecamp account with API access
- Basecamp project and To-do list set up

### 1. <PERSON><PERSON> and Install

```bash
git clone <repository-url>
cd tracker

# Install frontend dependencies
npm install

# Install backend dependencies
cd server
npm install
cd ..
```

### 2. Configure Basecamp Integration

1. **Register your app with Basecamp**:
   - Go to [Basecamp Launchpad](https://launchpad.37signals.com/integrations)
   - Create a new integration
   - Note your Client ID and Client Secret

2. **Set up environment variables**:
   ```bash
   cd server
   cp .env.example .env
   ```

3. **Edit `server/.env`** with your credentials:
   ```env
   # Basecamp OAuth 2.0 Configuration
   BASECAMP_CLIENT_ID=your_client_id_here
   BASECAMP_CLIENT_SECRET=your_client_secret_here
   BASECAMP_ACCOUNT_ID=your_account_id_here
   BASECAMP_PROJECT_ID=your_project_id_here
   BASECAMP_TODOLIST_ID=your_todolist_id_here

   # Basecamp User Credentials
   BASECAMP_USERNAME=<EMAIL>
   BASECAMP_PASSWORD=yded$H4-zK6xq+8
   ```

### 3. Find Your Basecamp IDs

Start the server and use the debug endpoints:

```bash
# Start the backend server
cd server
npm run dev

# In another terminal, get your account ID from Basecamp URL
# Example: https://3.basecamp.com/1234567/projects
# Your account ID is: 1234567

# Get your projects
curl http://localhost:3001/api/bugs/projects

# Get todo lists for a project
curl http://localhost:3001/api/bugs/projects/PROJECT_ID/todolists
```

### 4. Run the Application

```bash
# Terminal 1: Start backend server
cd server
npm run dev

# Terminal 2: Start frontend
npm run dev
```

The app will be available at `http://localhost:5173`

## 🔐 Authentication Setup

1. **Initial Authentication**:
   - Visit the app in your browser
   - Click "Connect to Basecamp" if not authenticated
   - Complete OAuth flow in the popup window
   - Tokens are automatically stored and refreshed

2. **Token Management**:
   - Access tokens are automatically refreshed
   - Tokens stored securely in `server/.tokens.json`
   - No manual token management required

## 📱 Usage

### Submitting a Bug

1. Fill out the bug title (required)
2. Add detailed description (optional)
3. Select priority level (Low/Medium/High)
4. Upload screenshot (optional) - drag & drop supported
5. Click "Submit Bug Report"

### Viewing Bugs

- Switch to "View Bugs" tab
- See all submitted bugs with status

## 🛠️ Development

### Project Structure

```
tracker/
├── src/                    # React frontend
│   ├── components/         # React components
│   ├── services/          # API services
│   └── styles/            # CSS styles
├── server/                # Node.js backend
│   ├── config/            # Configuration
│   ├── middleware/        # Express middleware
│   ├── routes/            # API routes
│   └── services/          # Business logic
└── public/                # Static assets
```

### Available Scripts

**Frontend:**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

**Backend:**
```bash
npm run dev          # Start with nodemon (auto-reload)
npm start            # Start production server
```

### API Endpoints

- `POST /api/bugs/submit` - Submit new bug
- `GET /api/bugs/list` - Get submitted bugs
- `GET /auth/status` - Check authentication status
- `GET /auth/url` - Get OAuth URL
- `GET /health` - Server health check

## 🔒 Security Features

- OAuth 2.0 authentication with automatic token refresh
- All Basecamp credentials stored server-side only
- CORS protection and rate limiting
- Input validation and file type restrictions
- Helmet.js security headers
- No sensitive data exposed to frontend
