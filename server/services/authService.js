import axios from 'axios';
import fs from 'fs/promises';
import path from 'path';
import { config } from '../config/config.js';

class AuthService {
  constructor() {
    this.tokenFilePath = path.join(process.cwd(), '.tokens.json');
    this.tokens = {
      accessToken: null,
      refreshToken: null,
      expiresAt: null
    };
    this.refreshTimer = null;
    this.isRefreshing = false;
    this.loadTokens();
  }

  async loadTokens() {
    try {
      const tokenData = await fs.readFile(this.tokenFilePath, 'utf8');
      this.tokens = JSON.parse(tokenData);
      console.log('🔑 Loaded existing tokens from file');
      this.logTokenStatus();
      this.scheduleTokenRefresh();
    } catch (error) {
      console.log('🔑 No existing tokens found, will need to authenticate');
    }
  }

  async saveTokens() {
    try {
      await fs.writeFile(this.tokenFilePath, JSON.stringify(this.tokens, null, 2));
      console.log('🔑 Tokens saved to file');
      this.logTokenStatus();
      this.scheduleTokenRefresh();
    } catch (error) {
      console.error('❌ Error saving tokens:', error);
    }
  }

  getAuthUrl() {
    const params = new URLSearchParams({
      type: 'web_server',
      client_id: config.basecamp.clientId,
      redirect_uri: config.basecamp.redirectUri
    });
    
    return `${config.basecamp.authUrl}?${params.toString()}`;
  }

  async exchangeCodeForToken(code) {
    try {
      const params = new URLSearchParams({
        type: 'web_server',
        client_id: config.basecamp.clientId,
        client_secret: config.basecamp.clientSecret,
        redirect_uri: config.basecamp.redirectUri,
        code: code
      });

      const response = await axios.post(config.basecamp.tokenUrl, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const { access_token, refresh_token, expires_in } = response.data;
      
      this.tokens = {
        accessToken: access_token,
        refreshToken: refresh_token,
        expiresAt: Date.now() + (expires_in * 1000)
      };

      await this.saveTokens();
      console.log('✅ New tokens obtained and scheduled for refresh');
      return this.tokens;
    } catch (error) {
      console.error('Error exchanging code for token:', error.response?.data || error.message);
      throw error;
    }
  }

  async refreshAccessToken() {
    if (!this.tokens.refreshToken) {
      throw new Error('No refresh token available');
    }

    if (this.isRefreshing) {
      console.log('🔄 Token refresh already in progress, waiting...');
      // Wait for current refresh to complete
      while (this.isRefreshing) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.tokens.accessToken;
    }

    this.isRefreshing = true;
    console.log('🔄 Refreshing access token...');

    try {
      const params = new URLSearchParams({
        type: 'refresh',
        client_id: config.basecamp.clientId,
        client_secret: config.basecamp.clientSecret,
        refresh_token: this.tokens.refreshToken
      });

      const response = await axios.post(config.basecamp.tokenUrl, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const { access_token, expires_in } = response.data;

      this.tokens.accessToken = access_token;
      this.tokens.expiresAt = Date.now() + (expires_in * 1000);

      await this.saveTokens();
      console.log('✅ Access token refreshed successfully');
      return this.tokens.accessToken;
    } catch (error) {
      console.error('❌ Error refreshing token:', error.response?.data || error.message);
      throw error;
    } finally {
      this.isRefreshing = false;
    }
  }

  async getValidAccessToken() {
    // Check if token exists and is not expired (with 5 minute buffer)
    const bufferTime = 5 * 60 * 1000; // 5 minutes
    if (this.tokens.accessToken && this.tokens.expiresAt > Date.now() + bufferTime) {
      return this.tokens.accessToken;
    }

    console.log('🔄 Access token expired or expiring soon, attempting refresh...');

    // Try to refresh if we have a refresh token
    if (this.tokens.refreshToken) {
      try {
        return await this.refreshAccessToken();
      } catch (error) {
        console.error('❌ Failed to refresh token:', error);
        // Clear invalid tokens
        this.tokens = {
          accessToken: null,
          refreshToken: null,
          expiresAt: null
        };
        await this.saveTokens();
      }
    }

    throw new Error('No valid access token available. Please re-authenticate.');
  }

  isAuthenticated() {
    return this.tokens.accessToken && this.tokens.expiresAt > Date.now();
  }

  logTokenStatus() {
    if (!this.tokens.accessToken) {
      console.log('🔑 Token Status: No access token');
      return;
    }

    const now = Date.now();
    const expiresAt = new Date(this.tokens.expiresAt);
    const timeUntilExpiry = this.tokens.expiresAt - now;
    const hoursUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60 * 60));
    const minutesUntilExpiry = Math.floor((timeUntilExpiry % (1000 * 60 * 60)) / (1000 * 60));

    if (timeUntilExpiry > 0) {
      console.log(`🔑 Token Status: Valid, expires in ${hoursUntilExpiry}h ${minutesUntilExpiry}m (${expiresAt.toLocaleString()})`);
    } else {
      console.log(`🔑 Token Status: EXPIRED ${Math.abs(hoursUntilExpiry)}h ${Math.abs(minutesUntilExpiry)}m ago`);
    }
  }

  scheduleTokenRefresh() {
    // Clear existing timer
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }

    if (!this.tokens.accessToken || !this.tokens.expiresAt) {
      return;
    }

    const now = Date.now();
    const expiresAt = this.tokens.expiresAt;
    const timeUntilExpiry = expiresAt - now;

    // Schedule refresh 10 minutes before expiry (or immediately if less than 10 minutes left)
    const refreshBuffer = 10 * 60 * 1000; // 10 minutes
    const timeUntilRefresh = Math.max(0, timeUntilExpiry - refreshBuffer);

    console.log(`⏰ Scheduling token refresh in ${Math.floor(timeUntilRefresh / (1000 * 60))} minutes`);

    this.refreshTimer = setTimeout(async () => {
      try {
        console.log('⏰ Scheduled token refresh triggered');
        await this.refreshAccessToken();
      } catch (error) {
        console.error('❌ Scheduled token refresh failed:', error);
        // Retry in 5 minutes
        setTimeout(() => this.scheduleTokenRefresh(), 5 * 60 * 1000);
      }
    }, timeUntilRefresh);
  }

  // Method to manually trigger token refresh (useful for testing)
  async forceRefresh() {
    console.log('🔄 Forcing token refresh...');
    return await this.refreshAccessToken();
  }

  // Get token expiry information
  getTokenInfo() {
    if (!this.tokens.accessToken) {
      return { authenticated: false, message: 'No access token' };
    }

    const now = Date.now();
    const timeUntilExpiry = this.tokens.expiresAt - now;
    const isExpired = timeUntilExpiry <= 0;
    const expiresAt = new Date(this.tokens.expiresAt);

    return {
      authenticated: !isExpired,
      expiresAt: expiresAt.toISOString(),
      timeUntilExpiryMs: timeUntilExpiry,
      timeUntilExpiryHuman: this.formatDuration(timeUntilExpiry),
      isExpired
    };
  }

  formatDuration(ms) {
    if (ms <= 0) return 'Expired';

    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }
}

export default new AuthService();
