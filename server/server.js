import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import path from 'path';
import { fileURLToPath } from 'url';
import { config } from './config/config.js';
import authService from './services/authService.js';
import bugsRouter from './routes/bugs.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: config.security.corsOrigin,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    authenticated: authService.isAuthenticated()
  });
});

// Authentication routes
app.get('/auth/url', (req, res) => {
  try {
    const authUrl = authService.getAuthUrl();
    res.json({ authUrl });
  } catch (error) {
    console.error('Error generating auth URL:', error);
    res.status(500).json({ error: 'Failed to generate auth URL' });
  }
});

app.get('/auth/callback', async (req, res) => {
  try {
    const { code } = req.query;
    
    if (!code) {
      return res.status(400).json({ error: 'Authorization code is required' });
    }

    await authService.exchangeCodeForToken(code);
    
    res.json({
      success: true,
      message: 'Authentication successful',
      redirectUrl: config.security.corsOrigin
    });
  } catch (error) {
    console.error('Auth callback error:', error);
    res.status(500).json({
      error: 'Authentication failed',
      message: error.message
    });
  }
});

app.get('/auth/status', (req, res) => {
  const tokenInfo = authService.getTokenInfo();
  res.json({
    authenticated: authService.isAuthenticated(),
    authUrl: authService.getAuthUrl(),
    tokenInfo
  });
});

// New endpoint to manually refresh token
app.post('/auth/refresh', async (req, res) => {
  try {
    const newToken = await authService.forceRefresh();
    res.json({
      success: true,
      message: 'Token refreshed successfully',
      tokenInfo: authService.getTokenInfo()
    });
  } catch (error) {
    console.error('Manual token refresh failed:', error);
    res.status(500).json({
      success: false,
      error: 'Token refresh failed',
      message: error.message
    });
  }
});

// New endpoint to get detailed token information
app.get('/auth/token-info', (req, res) => {
  res.json(authService.getTokenInfo());
});

// API routes
app.use('/api/bugs', bugsRouter);

// Serve static files from the React app build directory
if (config.nodeEnv === 'production') {
  app.use(express.static(path.join(__dirname, '../dist')));
}

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  
  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(413).json({
      error: 'File too large',
      message: 'Screenshot must be less than 10MB'
    });
  }
  
  if (error.message === 'Only image files are allowed') {
    return res.status(400).json({
      error: 'Invalid file type',
      message: 'Only image files are allowed for screenshots'
    });
  }

  res.status(500).json({
    error: 'Internal server error',
    message: config.nodeEnv === 'development' ? error.message : 'Something went wrong'
  });
});

// Catch all handler: send back React's index.html file for client-side routing
app.get('*', (req, res) => {
  if (config.nodeEnv === 'production') {
    res.sendFile(path.join(__dirname, '../dist/index.html'));
  } else {
    res.status(404).json({
      error: 'Not found',
      message: 'The requested endpoint does not exist'
    });
  }
});

// Start server
const PORT = config.port;
app.listen(PORT, () => {
  console.log(`🚀 Bug Tracker Server running on port ${PORT}`);
  console.log(`🔗 Frontend URL: ${config.security.corsOrigin}`);
  console.log(`🔐 Authenticated: ${authService.isAuthenticated()}`);
  
  if (!authService.isAuthenticated()) {
    console.log(`🔑 Auth URL: ${authService.getAuthUrl()}`);
  }
});

export default app;
