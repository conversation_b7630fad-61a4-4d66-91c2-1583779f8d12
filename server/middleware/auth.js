import authService from '../services/authService.js';

export const requireAuth = async (req, res, next) => {
  try {
    if (!authService.isAuthenticated()) {
      return res.status(401).json({
        error: 'Not authenticated with Basecamp',
        authUrl: authService.getAuthUrl()
      });
    }
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({ error: 'Authentication error' });
  }
};

export const optionalAuth = async (req, res, next) => {
  try {
    req.isAuthenticated = authService.isAuthenticated();
    next();
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    req.isAuthenticated = false;
    next();
  }
};
