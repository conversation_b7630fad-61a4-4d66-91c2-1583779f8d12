import dotenv from 'dotenv';

dotenv.config();

export const config = {
  port: process.env.PORT || 3001,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  basecamp: {
    clientId: process.env.BASECAMP_CLIENT_ID,
    clientSecret: process.env.BASECAMP_CLIENT_SECRET,
    redirectUri: process.env.BASECAMP_REDIRECT_URI,
    accountId: process.env.BASECAMP_ACCOUNT_ID,
    projectId: process.env.BASECAMP_PROJECT_ID,
    todolistId: process.env.BASECAMP_TODOLIST_ID,
    username: process.env.BASECAMP_USERNAME,
    password: process.env.BASECAMP_PASSWORD,
    apiBaseUrl: 'https://3.basecampapi.com',
    authUrl: 'https://launchpad.37signals.com/authorization/new',
    tokenUrl: 'https://launchpad.37signals.com/authorization/token'
  },
  
  security: {
    jwtSecret: process.env.JWT_SECRET || 'fallback-secret-key',
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:5173'
  },
  
  tokens: {
    accessToken: process.env.BASECAMP_ACCESS_TOKEN,
    refreshToken: process.env.BASECAMP_REFRESH_TOKEN,
    expiresAt: process.env.TOKEN_EXPIRES_AT
  }
};

export default config;
